<template>
  <div class="sidebar-menu">
    <!-- 系统Logo区域 -->
    <div class="logo-section">
      <div class="logo-circle">
        <span class="logo-text">一体化平台<br/>管理</span>
      </div>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      :default-active="activeMenu"
      class="sidebar-nav"
      background-color="transparent"
      text-color="#333"
      active-text-color="#1890ff"
      @select="handleMenuSelect"
    >
      <!-- 首页 -->
      <el-menu-item index="/" class="menu-item">
        <el-icon><House /></el-icon>
        <span>首页</span>
      </el-menu-item>

      <!-- 个人空间 -->
      <el-menu-item index="/personal" class="menu-item">
        <el-icon><User /></el-icon>
        <span>个人空间</span>
      </el-menu-item>

      <!-- 历程空间 -->
      <el-menu-item index="/history" class="menu-item">
        <el-icon><Clock /></el-icon>
        <span>历程空间</span>
      </el-menu-item>

      <!-- 传阅申请 -->
      <el-menu-item index="/circulation" class="menu-item">
        <el-icon><Share /></el-icon>
        <span>传阅申请</span>
      </el-menu-item>

      <!-- 意见采纳 -->
      <el-menu-item index="/suggestion" class="menu-item">
        <el-icon><ChatDotRound /></el-icon>
        <span>意见采纳</span>
      </el-menu-item>

      <!-- 图文档案 -->
      <el-sub-menu index="archive" class="menu-item">
        <template #title>
          <el-icon><Folder /></el-icon>
          <span>图文档案</span>
        </template>
        <el-menu-item index="/archive/document" class="sub-menu-item">
          <el-icon><Document /></el-icon>
          <span>图文档案管理</span>
        </el-menu-item>
        <el-menu-item index="/archive/control" class="sub-menu-item">
          <el-icon><Lock /></el-icon>
          <span>受控管理</span>
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  House,
  User,
  Clock,
  Share,
  ChatDotRound,
  Folder,
  Document,
  Lock
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 定义emit事件
const emit = defineEmits<{
  menuSelect: [menuItem: any]
}>()

// 菜单选择处理
function handleMenuSelect(index: string) {
  router.push(index)
  emit('menuSelect', { index, path: index })
}
</script>

<style scoped>
.sidebar-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logo-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.logo-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.logo-text {
  font-size: 12px;
  font-weight: 600;
  line-height: 1.2;
}

.sidebar-nav {
  flex: 1;
  border: none;
  padding: 10px 0;
}

.menu-item {
  margin: 4px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background-color: #f0f9ff !important;
}

.menu-item.is-active {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

.sub-menu-item {
  margin: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.sub-menu-item:hover {
  background-color: #f0f9ff !important;
}

.sidebar-nav :deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  padding-left: 20px !important;
}

.sidebar-nav :deep(.el-sub-menu .el-menu-item) {
  height: 40px;
  line-height: 40px;
  padding-left: 40px !important;
}

.sidebar-nav :deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  padding-left: 20px !important;
}

.sidebar-nav :deep(.el-menu-item.is-active) {
  background-color: #e6f7ff;
  color: #1890ff;
}

.sidebar-nav :deep(.el-menu-item:hover) {
  background-color: #f0f9ff;
}

.sidebar-nav :deep(.el-sub-menu__title:hover) {
  background-color: #f0f9ff;
}

.sidebar-nav :deep(.el-icon) {
  margin-right: 8px;
  font-size: 16px;
}
</style>
