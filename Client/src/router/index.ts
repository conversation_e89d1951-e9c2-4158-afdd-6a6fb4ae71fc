import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'Home',
          component: () => import('@/views/HomePage.vue'),
          meta: { title: '首页' }
        },
        {
          path: '/personal',
          name: 'PersonalSpace',
          component: () => import('@/views/PersonalSpace.vue'),
          meta: { title: '个人空间' }
        },
        {
          path: '/history',
          name: 'HistorySpace',
          component: () => import('@/views/HistorySpace.vue'),
          meta: { title: '历程空间' }
        },
        {
          path: '/circulation',
          name: 'Circulation',
          component: () => import('@/views/Circulation.vue'),
          meta: { title: '传阅申请' }
        },
        {
          path: '/suggestion',
          name: 'Suggestion',
          component: () => import('@/views/Suggestion.vue'),
          meta: { title: '意见采纳' }
        },
        {
          path: '/archive/document',
          name: 'ArchiveDocument',
          component: () => import('@/views/ArchiveDocument.vue'),
          meta: { title: '图文档案管理' }
        },
        {
          path: '/archive/control',
          name: 'ArchiveControl',
          component: () => import('@/views/ArchiveControl.vue'),
          meta: { title: '受控管理' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue')
    }
  ],
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 空间应用中心`
  }
  next()
})

export default router
