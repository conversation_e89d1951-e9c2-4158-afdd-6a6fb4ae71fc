<template>
  <div class="personal-space">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">个人空间</h2>
    </div>

    <!-- 个人信息卡片 -->
    <div class="profile-section">
      <el-card class="profile-card">
        <div class="profile-header">
          <el-avatar :size="80">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="profile-info">
            <h3>{{ userInfo.name }}</h3>
            <p>{{ userInfo.department }} - {{ userInfo.position }}</p>
            <p>工号：{{ userInfo.employeeId }}</p>
          </div>
          <div class="profile-actions">
            <el-button type="primary" @click="handleEditProfile">编辑资料</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能区域 -->
    <div class="function-section">
      <el-row :gutter="20">
        <!-- 个人信息管理 -->
        <el-col :span="8">
          <el-card class="function-card">
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>个人信息管理</span>
            </div>
            <div class="card-content">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ userInfo.name }}</span>
              </div>
              <div class="info-item">
                <label>部门：</label>
                <span>{{ userInfo.department }}</span>
              </div>
              <div class="info-item">
                <label>职位：</label>
                <span>{{ userInfo.position }}</span>
              </div>
              <div class="info-item">
                <label>邮箱：</label>
                <span>{{ userInfo.email }}</span>
              </div>
              <div class="info-item">
                <label>电话：</label>
                <span>{{ userInfo.phone }}</span>
              </div>
            </div>
            <div class="card-actions">
              <el-button type="primary" size="small" @click="handleEditInfo">修改信息</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 我的文档 -->
        <el-col :span="8">
          <el-card class="function-card">
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>我的文档</span>
            </div>
            <div class="card-content">
              <div class="doc-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ docStats.total }}</div>
                  <div class="stat-label">总文档数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ docStats.pending }}</div>
                  <div class="stat-label">待处理</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ docStats.completed }}</div>
                  <div class="stat-label">已完成</div>
                </div>
              </div>
            </div>
            <div class="card-actions">
              <el-button type="primary" size="small" @click="handleViewDocs">查看文档</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 我的任务 -->
        <el-col :span="8">
          <el-card class="function-card">
            <div class="card-header">
              <el-icon><List /></el-icon>
              <span>我的任务</span>
            </div>
            <div class="card-content">
              <div class="task-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ taskStats.total }}</div>
                  <div class="stat-label">总任务数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ taskStats.pending }}</div>
                  <div class="stat-label">待办理</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ taskStats.completed }}</div>
                  <div class="stat-label">已办理</div>
                </div>
              </div>
            </div>
            <div class="card-actions">
              <el-button type="primary" size="small" @click="handleViewTasks">查看任务</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>最近活动</span>
          </div>
        </template>

        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :type="activity.type"
          >
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>

        <div v-if="recentActivities.length === 0" class="no-activity">
          <el-empty description="暂无活动记录" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  User,
  Document,
  List,
  Clock
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 用户信息
const userInfo = reactive({
  name: 'MES用户',
  department: '技术部',
  position: '高级工程师',
  employeeId: 'EMP001',
  email: '<EMAIL>',
  phone: '138****8888'
})

// 文档统计
const docStats = reactive({
  total: 25,
  pending: 3,
  completed: 22
})

// 任务统计
const taskStats = reactive({
  total: 15,
  pending: 5,
  completed: 10
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '完成档案审核',
    description: '审核了档案编号为ARC001的技术文档',
    time: '2025-01-15 14:30',
    type: 'success'
  },
  {
    id: 2,
    title: '提交申请',
    description: '提交了新的传阅申请',
    time: '2025-01-15 10:20',
    type: 'primary'
  },
  {
    id: 3,
    title: '更新个人信息',
    description: '更新了联系方式',
    time: '2025-01-14 16:45',
    type: 'info'
  }
])

// 事件处理函数
function handleEditProfile() {
  ElMessage.info('编辑个人资料功能')
}

function handleEditInfo() {
  ElMessage.info('修改个人信息功能')
}

function handleViewDocs() {
  ElMessage.info('查看我的文档功能')
}

function handleViewTasks() {
  ElMessage.info('查看我的任务功能')
}
</script>

<style scoped>
.personal-space {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.profile-section {
  margin-bottom: 20px;
}

.profile-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.profile-card :deep(.el-card__body) {
  padding: 30px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.profile-info {
  flex: 1;
}

.profile-info h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.profile-info p {
  margin: 4px 0;
  opacity: 0.9;
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.function-section {
  margin-bottom: 20px;
}

.function-card {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.card-content {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item label {
  width: 60px;
  color: #666;
  font-size: 14px;
}

.info-item span {
  color: #333;
  font-size: 14px;
}

.doc-stats,
.task-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.card-actions {
  text-align: center;
}

.activity-section {
  background: white;
}

.activity-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
}

.activity-content p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.no-activity {
  text-align: center;
  padding: 40px;
}
</style>
