<template>
  <div class="archive-control">
    <div class="page-header">
      <h2 class="page-title">受控管理</h2>
      <el-button type="primary" :icon="Plus" @click="handleAdd">新增受控</el-button>
    </div>

    <el-card>
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <label>受控状态：</label>
            <el-select v-model="filters.status" placeholder="请选择" clearable>
              <el-option label="受控中" value="controlled" />
              <el-option label="已解除" value="released" />
              <el-option label="待审核" value="pending" />
            </el-select>
          </div>
          <div class="filter-item">
            <label>受控类型：</label>
            <el-select v-model="filters.type" placeholder="请选择" clearable>
              <el-option label="技术文档" value="technical" />
              <el-option label="管理文档" value="management" />
              <el-option label="图纸文件" value="drawing" />
            </el-select>
          </div>
          <div class="filter-actions">
            <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
            <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <el-table :data="tableData" style="width: 100%" stripe border>
        <el-table-column prop="id" label="受控编号" width="120" />
        <el-table-column prop="docName" label="文档名称" min-width="200" />
        <el-table-column prop="type" label="受控类型" width="120" />
        <el-table-column prop="controller" label="受控人" width="120" />
        <el-table-column prop="controlTime" label="受控时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleRelease(row)">解除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const filters = reactive({
  status: '',
  type: ''
})

const tableData = ref([])

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'controlled': 'warning',
    'released': 'success',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'controlled': '受控中',
    'released': '已解除',
    'pending': '待审核'
  }
  return textMap[status] || '未知'
}

function handleAdd() {
  ElMessage.info('新增受控功能')
}

function handleSearch() {
  ElMessage.info('查询功能')
}

function handleReset() {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
}

function handleView(row: any) {
  ElMessage.info(`查看受控: ${row.docName}`)
}

function handleRelease(row: any) {
  ElMessage.info(`解除受控: ${row.docName}`)
}
</script>

<style scoped>
.archive-control {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.filter-item .el-select {
  width: 150px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
</style>
