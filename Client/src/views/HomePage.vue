<template>
  <div class="home-page">
    <!-- 我的任务统计卡片 -->
    <div class="task-cards">
      <div class="task-section">
        <h3 class="section-title">
          <el-icon><Bell /></el-icon>
          我的任务
        </h3>

        <div class="task-stats">
          <!-- 待我办理 -->
          <div class="task-card pending">
            <div class="card-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">待我办理</div>
              <div class="card-number">{{ taskStats.pending }}</div>
            </div>
          </div>

          <!-- 我已办理 -->
          <div class="task-card completed">
            <div class="card-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">我已办理</div>
              <div class="card-number">{{ taskStats.completed }}</div>
            </div>
          </div>

          <!-- 我发起的 -->
          <div class="task-card initiated">
            <div class="card-icon">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">我发起的</div>
              <div class="card-number">{{ taskStats.initiated }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="task-list-section">
        <div class="task-list-header">
          <span>序号</span>
          <span>操作</span>
          <span>流程类型</span>
          <span>流程名称</span>
          <span>发起人员</span>
          <span>流程状态</span>
          <span>接收时间</span>
          <span>发起时间</span>
        </div>

        <div class="no-data">
          <el-empty description="暂无数据" />
        </div>
      </div>
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-section">
      <!-- 型号变更信息统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <el-icon><PieChart /></el-icon>
          <span>型号变更信息统计</span>
          <el-select v-model="selectedYear1" size="small" style="width: 80px;">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
          </el-select>
        </div>
        <div class="chart-content">
          <div class="chart-placeholder">
            <el-icon><PieChart /></el-icon>
            <span>图表区域</span>
          </div>
        </div>
      </div>

      <!-- 型号偏离信息统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <el-icon><Histogram /></el-icon>
          <span>型号偏离信息统计</span>
          <el-select v-model="selectedYear2" size="small" style="width: 80px;">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
          </el-select>
        </div>
        <div class="chart-content">
          <div class="chart-placeholder">
            <el-icon><Histogram /></el-icon>
            <span>图表区域</span>
          </div>
        </div>
      </div>

      <!-- 型号超差信息统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <el-icon><TrendCharts /></el-icon>
          <span>型号超差信息统计</span>
          <el-select v-model="selectedYear3" size="small" style="width: 80px;">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
          </el-select>
        </div>
        <div class="chart-content">
          <div class="chart-placeholder">
            <el-icon><TrendCharts /></el-icon>
            <span>图表区域</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作引导和通知消息 -->
    <div class="bottom-section">
      <!-- 操作引导 -->
      <div class="operation-guide">
        <el-card>
          <template #header>
            <div class="section-header">
              <el-icon><Guide /></el-icon>
              <span>操作引导</span>
              <el-button type="text" size="small" style="margin-left: auto;">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="guide-content">
            <div class="guide-flow">
              <div class="flow-step start-step">
                <div class="step-icon">开</div>
                <div class="step-label">开始</div>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step">
                <div class="step-icon personal">个</div>
                <div class="step-label">个人空间</div>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step">
                <div class="step-icon">库</div>
                <div class="step-label">库房</div>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step">
                <div class="step-icon">装</div>
                <div class="step-label">装调</div>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step">
                <div class="step-icon">入</div>
                <div class="step-label">入库</div>
              </div>
            </div>
            <div class="guide-actions">
              <el-button type="primary" size="small">查看</el-button>
              <el-button size="small">变更</el-button>
              <el-button size="small">质量</el-button>
              <el-button size="small">质量</el-button>
              <el-button size="small">技术状态查询</el-button>
            </div>
            <div class="guide-footer">
              <span>有限责任公司</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 通知消息 -->
      <div class="notification-panel">
        <el-card>
          <template #header>
            <div class="section-header">
              <el-icon><Bell /></el-icon>
              <span>通知消息</span>
              <div class="notification-tabs">
                <el-button
                  type="primary"
                  size="small"
                  :class="{ active: activeTab === 'system' }"
                  @click="activeTab = 'system'"
                >
                  系统消息
                </el-button>
                <el-button
                  size="small"
                  :class="{ active: activeTab === 'business' }"
                  @click="activeTab = 'business'"
                >
                  业务消息
                </el-button>
              </div>
            </div>
          </template>
          <div class="notification-content">
            <div v-if="activeTab === 'system'" class="message-list">
              <div v-for="msg in systemMessages" :key="msg.id" class="message-item">
                <div class="message-icon">
                  <el-icon><InfoFilled /></el-icon>
                </div>
                <div class="message-content">
                  <div class="message-title">{{ msg.title }}</div>
                  <div class="message-time">{{ msg.time }}</div>
                </div>
              </div>
            </div>
            <div v-else class="message-list">
              <div v-for="msg in businessMessages" :key="msg.id" class="message-item">
                <div class="message-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="message-content">
                  <div class="message-title">{{ msg.title }}</div>
                  <div class="message-time">{{ msg.time }}</div>
                </div>
              </div>
            </div>
            <div class="notification-footer">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  Bell,
  Clock,
  Check,
  Plus,
  PieChart,
  Histogram,
  TrendCharts,
  Guide,
  MoreFilled,
  InfoFilled,
  Document
} from '@element-plus/icons-vue'

// 任务统计数据
const taskStats = reactive({
  pending: 0,
  completed: 0,
  initiated: 0
})

// 图表年份选择
const selectedYear1 = ref('2025')
const selectedYear2 = ref('2025')
const selectedYear3 = ref('2025')

// 通知消息相关
const activeTab = ref('system')
const systemMessages = ref([
  { id: 1, title: '系统维护通知', time: '2025-01-15 10:30' },
  { id: 2, title: '版本更新提醒', time: '2025-01-14 16:20' },
  { id: 3, title: '安全策略更新', time: '2025-01-13 09:15' }
])
const businessMessages = ref([
  { id: 1, title: '文档审核任务', time: '2025-01-15 14:30' },
  { id: 2, title: '图纸归档提醒', time: '2025-01-14 11:45' },
  { id: 3, title: '质量检查通知', time: '2025-01-13 15:20' }
])
</script>

<style scoped>
.home-page {
  padding: 0;
}

.task-cards {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.task-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.task-card {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.task-card:hover {
  transform: translateY(-2px);
}

.task-card.pending {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.task-card.completed {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.task-card.initiated {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.pending .card-icon {
  background: #2196f3;
  color: white;
}

.completed .card-icon {
  background: #4caf50;
  color: white;
}

.initiated .card-icon {
  background: #ff9800;
  color: white;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-number {
  font-size: 32px;
  font-weight: 600;
  color: #333;
}

.task-list-section {
  margin-top: 20px;
}

.task-list-header {
  display: grid;
  grid-template-columns: 60px 80px 120px 200px 120px 120px 150px 150px;
  gap: 16px;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 4px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.no-data {
  padding: 40px;
  text-align: center;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.chart-header span {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-left: 8px;
}

.chart-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #ccc;
  font-size: 48px;
}

.chart-placeholder span {
  font-size: 14px;
}

/* 底部区域样式 */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

/* 操作引导样式 */
.operation-guide {
  min-height: 300px;
}

.guide-content {
  padding: 20px 0;
}

.guide-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.step-icon.personal {
  background: #52c41a;
}

.start-step .step-icon {
  background: #722ed1;
}

.step-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.flow-arrow {
  color: #1890ff;
  font-size: 16px;
  margin: 0 5px;
}

.guide-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.guide-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
}

/* 通知消息样式 */
.notification-panel {
  min-height: 300px;
}

.notification-tabs {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.notification-tabs .el-button {
  border-radius: 4px;
}

.notification-tabs .el-button.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.notification-content {
  min-height: 200px;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.message-item:hover {
  background: #e6f7ff;
}

.message-icon {
  color: #1890ff;
  font-size: 16px;
  margin-top: 2px;
}

.message-content {
  flex: 1;
}

.message-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.notification-footer {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bottom-section {
    grid-template-columns: 1fr;
  }

  .guide-flow {
    flex-direction: column;
    gap: 15px;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .guide-actions {
    justify-content: flex-start;
  }
}
</style>
