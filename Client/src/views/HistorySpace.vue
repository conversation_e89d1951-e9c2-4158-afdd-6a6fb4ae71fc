<template>
  <div class="history-space">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">历程空间</h2>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="handleAdd">新增记录</el-button>
        <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card>
        <div class="filter-row">
          <div class="filter-item">
            <label>图文类型：</label>
            <el-select v-model="filters.docType" placeholder="请选择" clearable>
              <el-option label="技术文档" value="technical" />
              <el-option label="管理文档" value="management" />
              <el-option label="图纸文件" value="drawing" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>图文类别：</label>
            <el-select v-model="filters.docCategory" placeholder="请选择" clearable>
              <el-option label="设计图纸" value="design" />
              <el-option label="工艺文件" value="process" />
              <el-option label="检验文件" value="inspection" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>任务类型：</label>
            <el-select v-model="filters.taskType" placeholder="请选择" clearable>
              <el-option label="审核任务" value="review" />
              <el-option label="归档任务" value="archive" />
              <el-option label="更新任务" value="update" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>任务来源：</label>
            <el-select v-model="filters.taskSource" placeholder="请选择" clearable>
              <el-option label="系统生成" value="system" />
              <el-option label="手动创建" value="manual" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>任务名称：</label>
            <el-select v-model="filters.taskName" placeholder="请选择" clearable>
              <el-option label="文档审核" value="doc_review" />
              <el-option label="图纸归档" value="drawing_archive" />
            </el-select>
          </div>

          <div class="filter-actions">
            <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
            <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 历程记录表格 -->
    <div class="table-section">
      <el-card>
        <!-- 表格工具栏 -->
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入关键词"
              style="width: 300px;"
              clearable
            >
              <template #append>
                <el-button :icon="Search" @click="handleTableSearch" />
              </template>
            </el-input>
          </div>
          <div class="toolbar-right">
            <el-button type="primary" size="small">高级查询</el-button>
            <el-button size="small">显示</el-button>
            <el-button size="small">详情</el-button>
            <el-button size="small">复制</el-button>
            <el-button size="small" :icon="Setting">设置</el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          border
          height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="sequence" label="序号" width="80" />
          <el-table-column prop="docType" label="图文类型" width="120" />
          <el-table-column prop="docCategory" label="图文类别" width="120" />
          <el-table-column prop="docName" label="图文名称" min-width="200" />
          <el-table-column prop="taskType" label="任务类型" width="120" />
          <el-table-column prop="taskSource" label="任务来源" width="120" />
          <el-table-column prop="taskName" label="任务名称" width="120" />
          <el-table-column prop="responsible" label="负责人员" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Plus,
  Refresh,
  Search,
  Setting
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 筛选条件
const filters = reactive({
  docType: '',
  docCategory: '',
  taskType: '',
  taskSource: '',
  taskName: ''
})

// 搜索关键词
const searchKeyword = ref('')

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 获取状态类型
function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || '未知'
}

// 事件处理函数
function handleAdd() {
  ElMessage.info('新增历程记录功能')
}

function handleRefresh() {
  loadData()
}

function handleSearch() {
  pagination.currentPage = 1
  loadData()
}

function handleReset() {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
  searchKeyword.value = ''
  loadData()
}

function handleTableSearch() {
  pagination.currentPage = 1
  loadData()
}

function handleView(row: any) {
  ElMessage.info(`查看历程: ${row.docName}`)
}

function handleEdit(row: any) {
  ElMessage.info(`编辑历程: ${row.docName}`)
}

function handleSizeChange(size: number) {
  pagination.pageSize = size
  loadData()
}

function handleCurrentChange(page: number) {
  pagination.currentPage = page
  loadData()
}

// 加载数据
function loadData() {
  // 模拟数据加载
  console.log('加载历程数据', { filters, searchKeyword: searchKeyword.value, pagination })
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.history-space {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.filter-item .el-select {
  width: 150px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.table-section {
  background: white;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
