<template>
  <div class="suggestion">
    <div class="page-header">
      <h2 class="page-title">意见采纳</h2>
      <el-button type="primary" :icon="Plus" @click="handleAdd">提交意见</el-button>
    </div>

    <el-card>
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <label>意见状态：</label>
            <el-select v-model="filters.status" placeholder="请选择" clearable>
              <el-option label="待处理" value="pending" />
              <el-option label="已采纳" value="adopted" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </div>
          <div class="filter-item">
            <label>意见类型：</label>
            <el-select v-model="filters.type" placeholder="请选择" clearable>
              <el-option label="功能建议" value="feature" />
              <el-option label="问题反馈" value="bug" />
              <el-option label="流程优化" value="process" />
            </el-select>
          </div>
          <div class="filter-actions">
            <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
            <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <el-table :data="tableData" style="width: 100%" stripe border>
        <el-table-column prop="id" label="意见编号" width="120" />
        <el-table-column prop="title" label="意见标题" min-width="200" />
        <el-table-column prop="type" label="意见类型" width="120" />
        <el-table-column prop="submitter" label="提交人" width="120" />
        <el-table-column prop="submitTime" label="提交时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleProcess(row)">处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const filters = reactive({
  status: '',
  type: ''
})

const tableData = ref([])

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'adopted': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'pending': '待处理',
    'adopted': '已采纳',
    'rejected': '已拒绝'
  }
  return textMap[status] || '未知'
}

function handleAdd() {
  ElMessage.info('提交意见功能')
}

function handleSearch() {
  ElMessage.info('查询功能')
}

function handleReset() {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
}

function handleView(row: any) {
  ElMessage.info(`查看意见: ${row.title}`)
}

function handleProcess(row: any) {
  ElMessage.info(`处理意见: ${row.title}`)
}
</script>

<style scoped>
.suggestion {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.filter-item .el-select {
  width: 150px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
</style>
