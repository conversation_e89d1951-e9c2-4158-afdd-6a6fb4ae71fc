<template>
  <div class="archive-document">
    <!-- 页面标题和操作区域 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">图文档案管理</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
        <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
      </div>
    </div>

    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <el-card>
        <div class="filter-row">
          <!-- 任务类型 -->
          <div class="filter-item">
            <label>任务类型：</label>
            <el-select v-model="filters.taskType" placeholder="请选择" clearable>
              <el-option label="档案整理" value="organize" />
              <el-option label="档案录入" value="input" />
              <el-option label="档案审核" value="review" />
            </el-select>
          </div>

          <!-- 任务状态 -->
          <div class="filter-item">
            <label>任务状态：</label>
            <el-select v-model="filters.status" placeholder="请选择" clearable>
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </div>

          <!-- 任务来源 -->
          <div class="filter-item">
            <label>任务来源：</label>
            <el-select v-model="filters.source" placeholder="请选择" clearable>
              <el-option label="系统生成" value="system" />
              <el-option label="手动创建" value="manual" />
            </el-select>
          </div>

          <!-- 任务名称 -->
          <div class="filter-item">
            <label>任务名称：</label>
            <el-select v-model="filters.taskName" placeholder="请选择" clearable>
              <el-option label="档案分类" value="classify" />
              <el-option label="档案编号" value="number" />
            </el-select>
          </div>
        </div>

        <div class="filter-row">
          <!-- 任务类别 -->
          <div class="filter-item">
            <label>任务类别：</label>
            <el-select v-model="filters.category" placeholder="请选择" clearable>
              <el-option label="图文档案" value="document" />
              <el-option label="电子档案" value="electronic" />
            </el-select>
          </div>

          <!-- 任务系统 -->
          <div class="filter-item">
            <label>任务系统：</label>
            <el-select v-model="filters.system" placeholder="请选择" clearable>
              <el-option label="档案管理系统" value="archive" />
              <el-option label="文档管理系统" value="document" />
            </el-select>
          </div>

          <!-- 任务负责人 -->
          <div class="filter-item">
            <label>任务负责人：</label>
            <el-select v-model="filters.responsible" placeholder="请选择" clearable>
              <el-option label="张三" value="zhangsan" />
              <el-option label="李四" value="lisi" />
            </el-select>
          </div>

          <!-- 操作按钮 -->
          <div class="filter-actions">
            <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
            <el-button :icon="Refresh" @click="handleReset">重置</el-button>
            <el-button :icon="Download" @click="handleExport">导出</el-button>
            <el-button :icon="Upload" @click="handleImport">导入</el-button>
            <el-button :icon="Setting" @click="handleSettings">设置</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <el-card>
        <!-- 表格工具栏 -->
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入档案名称"
              style="width: 300px;"
              clearable
            >
              <template #append>
                <el-button :icon="Search" @click="handleTableSearch" />
              </template>
            </el-input>
          </div>
          <div class="toolbar-right">
            <el-button type="primary" size="small">高级查询</el-button>
            <el-button size="small">显示</el-button>
            <el-button size="small">详情</el-button>
            <el-button size="small">复制</el-button>
            <el-button size="small" :icon="Setting">设置</el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          border
          height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="sequence" label="序号" width="80" />
          <el-table-column prop="archiveCode" label="档案编号" width="120" />
          <el-table-column prop="archiveType" label="档案类型" width="120" />
          <el-table-column prop="archiveName" label="档案名称" min-width="200" />
          <el-table-column prop="taskType" label="任务类型" width="120" />
          <el-table-column prop="taskSource" label="任务来源" width="120" />
          <el-table-column prop="taskCategory" label="任务类别" width="120" />
          <el-table-column prop="taskSystem" label="任务系统" width="120" />
          <el-table-column prop="responsible" label="负责人员" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Plus,
  Refresh,
  Search,
  Download,
  Upload,
  Setting
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 筛选条件
const filters = reactive({
  taskType: '',
  status: '',
  source: '',
  taskName: '',
  category: '',
  system: '',
  responsible: ''
})

// 搜索关键词
const searchKeyword = ref('')

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 获取状态类型
function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成'
  }
  return textMap[status] || '未知'
}

// 事件处理函数
function handleAdd() {
  ElMessage.info('新增功能')
}

function handleRefresh() {
  loadData()
}

function handleSearch() {
  pagination.currentPage = 1
  loadData()
}

function handleReset() {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
  searchKeyword.value = ''
  loadData()
}

function handleExport() {
  ElMessage.info('导出功能')
}

function handleImport() {
  ElMessage.info('导入功能')
}

function handleSettings() {
  ElMessage.info('设置功能')
}

function handleTableSearch() {
  pagination.currentPage = 1
  loadData()
}

function handleEdit(row: any) {
  ElMessage.info(`编辑: ${row.archiveName}`)
}

function handleDelete(row: any) {
  ElMessage.info(`删除: ${row.archiveName}`)
}

function handleSizeChange(size: number) {
  pagination.pageSize = size
  loadData()
}

function handleCurrentChange(page: number) {
  pagination.currentPage = page
  loadData()
}

// 加载数据
function loadData() {
  // 模拟数据加载
  console.log('加载数据', { filters, searchKeyword: searchKeyword.value, pagination })
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.archive-document {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.filter-item .el-select {
  width: 150px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.table-section {
  background: white;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
