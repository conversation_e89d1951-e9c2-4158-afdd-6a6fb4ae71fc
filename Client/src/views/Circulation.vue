<template>
  <div class="circulation">
    <div class="page-header">
      <h2 class="page-title">传阅申请</h2>
      <el-button type="primary" :icon="Plus" @click="handleAdd">新增申请</el-button>
    </div>

    <el-card>
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <label>申请状态：</label>
            <el-select v-model="filters.status" placeholder="请选择" clearable>
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </div>
          <div class="filter-item">
            <label>申请类型：</label>
            <el-select v-model="filters.type" placeholder="请选择" clearable>
              <el-option label="文档传阅" value="document" />
              <el-option label="图纸传阅" value="drawing" />
            </el-select>
          </div>
          <div class="filter-actions">
            <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
            <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <el-table :data="tableData" style="width: 100%" stripe border>
        <el-table-column prop="id" label="申请编号" width="120" />
        <el-table-column prop="title" label="申请标题" min-width="200" />
        <el-table-column prop="type" label="申请类型" width="120" />
        <el-table-column prop="applicant" label="申请人" width="120" />
        <el-table-column prop="createTime" label="申请时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const filters = reactive({
  status: '',
  type: ''
})

const tableData = ref([])

function getStatusType(status: string) {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return textMap[status] || '未知'
}

function handleAdd() {
  ElMessage.info('新增传阅申请功能')
}

function handleSearch() {
  ElMessage.info('查询功能')
}

function handleReset() {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = ''
  })
}

function handleView(row: any) {
  ElMessage.info(`查看申请: ${row.title}`)
}

function handleEdit(row: any) {
  ElMessage.info(`编辑申请: ${row.title}`)
}
</script>

<style scoped>
.circulation {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.filter-item .el-select {
  width: 150px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
</style>
