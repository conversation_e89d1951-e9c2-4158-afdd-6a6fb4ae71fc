<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-left">
        <div class="logo">
          <div class="logo-placeholder">
            <el-icon><Setting /></el-icon>
          </div>
          <span class="system-title">空间应用中心 - 图文&受控管理</span>
        </div>
      </div>

      <div class="header-center">
        <el-input
          v-model="searchText"
          placeholder="请输入检索内容"
          class="search-input"
          clearable
        >
          <template #append>
            <el-button :icon="Search" @click="handleSearch" />
          </template>
        </el-input>
      </div>

      <div class="header-right">
        <el-button type="danger" size="small">对中专项防控和科学防控</el-button>
        <el-button type="primary" size="small">严防输入</el-button>
        <el-button type="warning" size="small">坚持底线思维</el-button>

        <el-dropdown class="user-dropdown">
          <span class="user-info">
            <el-avatar :size="32">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="username">MES...</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人设置</el-dropdown-item>
              <el-dropdown-item>修改密码</el-dropdown-item>
              <el-dropdown-item divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 左侧导航菜单 -->
      <el-aside class="sidebar" :width="sidebarWidth">
        <SidebarMenu @menu-select="handleMenuSelect" />
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <!-- 面包屑导航 -->
        <el-breadcrumb class="breadcrumb" separator="/">
          <el-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
            <router-link v-if="item.path" :to="item.path">{{ item.title }}</router-link>
            <span v-else>{{ item.title }}</span>
          </el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 页面内容 -->
        <div class="page-content">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Search, Setting, User } from '@element-plus/icons-vue'
import SidebarMenu from '@/components/SidebarMenu.vue'

const route = useRoute()
const searchText = ref('')
const sidebarWidth = ref('240px')

// 面包屑导航数据
const breadcrumbItems = computed(() => {
  const items = [{ title: '首页', path: '/' }]

  // 根据当前路由生成面包屑
  if (route.path !== '/') {
    const pathSegments = route.path.split('/').filter(Boolean)
    pathSegments.forEach((segment, index) => {
      const path = '/' + pathSegments.slice(0, index + 1).join('/')
      items.push({
        title: getPageTitle(segment),
        path: index === pathSegments.length - 1 ? '' : path
      })
    })
  }

  return items
})

// 获取页面标题
function getPageTitle(segment: string): string {
  const titleMap: Record<string, string> = {
    'personal': '个人空间',
    'history': '历程空间',
    'circulation': '传阅申请',
    'suggestion': '意见采纳',
    'archive': '图文档案',
    'document': '图文档案管理'
  }
  return titleMap[segment] || segment
}

// 搜索处理
function handleSearch() {
  console.log('搜索:', searchText.value)
}

// 菜单选择处理
function handleMenuSelect(menuItem: any) {
  console.log('选择菜单:', menuItem)
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  background: #f0f2f5;
}

.header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.system-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.username {
  font-size: 14px;
}

.main-container {
  height: calc(100vh - 60px);
}

.sidebar {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
}

.main-content {
  background: #f0f2f5;
  padding: 0;
  overflow: auto;
}

.breadcrumb {
  background: white;
  padding: 12px 20px;
  margin: 0;
  border-bottom: 1px solid #e8e8e8;
}

.breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #666;
}

.page-content {
  padding: 20px;
  min-height: calc(100vh - 120px);
}
</style>
